// 基于ES模块的WebSocket单例管理器
// 实现"单例即服务"架构模式，提供全局唯一的WebSocket连接

import { featureFlagManager } from './featureFlags';

export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp?: number;
  sessionId?: string;
  userId?: string;
}

export interface ConnectionConfig {
  sessionId: string;
  interviewType: 'formal' | 'mock';
  interviewLanguage: 'zh' | 'en';
  answerStyle: 'detailed' | 'concise';
  selectedPositionId?: string;
}

export type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

export interface ConnectionMetrics {
  connectionCount: number;
  messagesSent: number;
  messagesReceived: number;
  lastConnectedAt: Date | null;
  lastDisconnectedAt: Date | null;
  reconnectAttempts: number;
  averageLatency: number;
  errorCount: number;
}

// 全局状态（ES模块级别的单例状态）
let ws: WebSocket | null = null;
let connectionState: ConnectionState = 'disconnected';
let currentSessionId: string | null = null;
let currentConfig: ConnectionConfig | null = null;
let messageQueue: WebSocketMessage[] = [];
let eventListeners: Map<string, ((data: any) => void)[]> = new Map();
let heartbeatInterval: NodeJS.Timeout | null = null;
let reconnectTimeout: NodeJS.Timeout | null = null;
let reconnectAttempts = 0;
let maxReconnectAttempts = 3; // 减少重连次数，避免过度重试
let reconnectDelay = 2000; // 增加重连延迟，减少频繁重连

// 连接指标
let metrics: ConnectionMetrics = {
  connectionCount: 0,
  messagesSent: 0,
  messagesReceived: 0,
  lastConnectedAt: null,
  lastDisconnectedAt: null,
  reconnectAttempts: 0,
  averageLatency: 0,
  errorCount: 0
};

// 延迟测量
let pingStartTime: number | null = null;
let latencyMeasurements: number[] = [];

/**
 * 获取正确的 WebSocket 服务基础 URL
 */
function getWsBaseUrl(): string {
  // 生产环境的判断：优先检查 hostname 是否为生产域名
  if (window.location.hostname === 'mianshijun.xyz' || import.meta.env.PROD) {
    const protocol = 'wss:';
    const host = window.location.host; // 这将是 'mianshijun.xyz'
    return `${protocol}//${host}`;
  }

  // 默认返回开发环境地址
  return 'ws://localhost:3000';
}

/**
 * 构建WebSocket连接URL
 */
function buildWebSocketUrl(sessionId: string): string {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found');
  }

  const baseUrl = getWsBaseUrl();
  const url = `${baseUrl}/api/ws/interview/${sessionId}?token=${encodeURIComponent(token)}`;

  // 为了调试，可以加一句日志
  console.log(`[webSocketManager] Connecting to: ${url}`);

  return url;
}

/**
 * 发送心跳ping消息
 */
function sendHeartbeat(): void {
  if (ws && ws.readyState === WebSocket.OPEN) {
    pingStartTime = Date.now();
    sendMessage({
      type: 'ping',
      timestamp: pingStartTime
    });
    
    if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
      console.log('💓 WebSocket: Heartbeat ping sent');
    }
  }
}

/**
 * 处理心跳pong响应
 */
function handlePongMessage(): void {
  if (pingStartTime) {
    const latency = Date.now() - pingStartTime;
    latencyMeasurements.push(latency);
    
    // 保持最近10次测量
    if (latencyMeasurements.length > 10) {
      latencyMeasurements.shift();
    }
    
    // 计算平均延迟
    metrics.averageLatency = latencyMeasurements.reduce((sum, lat) => sum + lat, 0) / latencyMeasurements.length;
    
    if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
      console.log(`💓 WebSocket: Heartbeat pong received, latency: ${latency}ms, avg: ${metrics.averageLatency.toFixed(1)}ms`);
    }
    
    pingStartTime = null;
  }
}

/**
 * 启动心跳机制
 */
function startHeartbeat(): void {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
  }
  
  heartbeatInterval = setInterval(sendHeartbeat, 30000); // 每30秒发送心跳
  
  if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
    console.log('💓 WebSocket: Heartbeat started');
  }
}

/**
 * 停止心跳机制
 */
function stopHeartbeat(): void {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
    
    if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
      console.log('💓 WebSocket: Heartbeat stopped');
    }
  }
}

/**
 * 处理WebSocket消息
 */
function handleMessage(event: MessageEvent): void {
  try {
    const message: WebSocketMessage = JSON.parse(event.data);
    metrics.messagesReceived++;
    
    if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
      console.log('📨 WebSocket: Message received:', message);
    }

    // 处理心跳响应
    if (message.type === 'pong') {
      handlePongMessage();
      return;
    }

    // 触发事件监听器
    const listeners = eventListeners.get(message.type) || [];
    listeners.forEach(callback => {
      try {
        callback(message.data || message);
      } catch (error) {
        console.error(`❌ WebSocket: Error in message handler for type '${message.type}':`, error);
        metrics.errorCount++;
      }
    });

    // 发射全局事件
    window.dispatchEvent(new CustomEvent('websocket-message', {
      detail: message
    }));

  } catch (error) {
    console.error('❌ WebSocket: Error parsing message:', error);
    metrics.errorCount++;
  }
}

/**
 * 处理连接打开
 */
function handleOpen(): void {
  console.log('🔗 WebSocket: Connection opened');
  connectionState = 'connected';
  reconnectAttempts = 0;
  metrics.connectionCount++;
  metrics.lastConnectedAt = new Date();

  // 启动心跳
  startHeartbeat();

  // 发送队列中的消息
  while (messageQueue.length > 0) {
    const queuedMessage = messageQueue.shift();
    if (queuedMessage) {
      sendMessage(queuedMessage);
    }
  }

  // 发射连接状态变化事件
  window.dispatchEvent(new CustomEvent('websocket-connection-change', {
    detail: { sessionId: currentSessionId, connected: true }
  }));
}

/**
 * 处理连接关闭
 */
function handleClose(event: CloseEvent): void {
  console.log('🔌 WebSocket: Connection closed:', event);
  connectionState = 'disconnected';
  metrics.lastDisconnectedAt = new Date();

  // 停止心跳
  stopHeartbeat();

  // 发射连接状态变化事件
  window.dispatchEvent(new CustomEvent('websocket-connection-change', {
    detail: { sessionId: currentSessionId, connected: false }
  }));

  // 尝试重连（如果不是主动关闭）
  if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
    attemptReconnect();
  }
}

/**
 * 处理连接错误
 */
function handleError(event: Event): void {
  console.error('❌ WebSocket: Connection error:', event);
  connectionState = 'error';
  metrics.errorCount++;

  // 发射错误事件
  window.dispatchEvent(new CustomEvent('websocket-error', {
    detail: { error: event, sessionId: currentSessionId }
  }));
}

/**
 * 尝试重连
 */
function attemptReconnect(): void {
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.error('❌ WebSocket: Max reconnect attempts reached');
    connectionState = 'error';
    return;
  }

  reconnectAttempts++;
  metrics.reconnectAttempts++;
  connectionState = 'reconnecting';

  const delay = reconnectDelay * Math.pow(2, reconnectAttempts - 1); // 指数退避
  
  console.log(`🔄 WebSocket: Attempting reconnect ${reconnectAttempts}/${maxReconnectAttempts} in ${delay}ms`);

  reconnectTimeout = setTimeout(() => {
    if (currentSessionId && currentConfig) {
      connect(currentSessionId, currentConfig);
    }
  }, delay);
}

/**
 * 建立WebSocket连接
 */
export async function connect(sessionId: string, config: ConnectionConfig): Promise<void> {
  // 检查功能开关
  if (!featureFlagManager.isEnabled('enable-unified-websocket')) {
    console.log('🚩 WebSocket: Unified WebSocket is disabled by feature flag');
    throw new Error('Unified WebSocket is disabled');
  }

  // 开发环境下减少连接频率，避免过度重连
  if (process.env.NODE_ENV === 'development') {
    const now = Date.now();
    const lastAttempt = metrics.lastConnectedAt || 0;
    const minInterval = 3000; // 最小3秒间隔

    if (now - lastAttempt < minInterval) {
      console.log('🚦 WebSocket: Rate limiting connection attempts in development');
      return;
    }
  }

  // 如果已有连接，先关闭
  if (ws) {
    disconnect();
  }

  try {
    connectionState = 'connecting';
    currentSessionId = sessionId;
    currentConfig = config;

    const wsUrl = buildWebSocketUrl(sessionId);
    console.log(`🔗 WebSocket: Connecting to ${wsUrl}`);

    ws = new WebSocket(wsUrl);

    // 设置事件处理器
    ws.onopen = handleOpen;
    ws.onmessage = handleMessage;
    ws.onclose = handleClose;
    ws.onerror = handleError;

    // 等待连接建立
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 10000);

      const handleConnectionResult = () => {
        clearTimeout(timeout);
        if (connectionState === 'connected') {
          resolve();
        } else {
          reject(new Error(`WebSocket connection failed: ${connectionState}`));
        }
      };

      // 监听连接状态变化
      const checkConnection = () => {
        if (connectionState === 'connected' || connectionState === 'error') {
          handleConnectionResult();
        } else {
          setTimeout(checkConnection, 100);
        }
      };

      checkConnection();
    });

  } catch (error) {
    console.error('❌ WebSocket: Connection failed:', error);
    connectionState = 'error';
    metrics.errorCount++;
    throw error;
  }
}

/**
 * 断开WebSocket连接
 */
export function disconnect(): void {
  console.log('🔌 WebSocket: Disconnecting');

  // 清理重连定时器
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }

  // 停止心跳
  stopHeartbeat();

  // 关闭连接
  if (ws) {
    ws.close(1000, 'Client disconnect');
    ws = null;
  }

  connectionState = 'disconnected';
  currentSessionId = null;
  currentConfig = null;
  reconnectAttempts = 0;
}

/**
 * 发送消息
 */
export function sendMessage(message: WebSocketMessage): void {
  if (!message.timestamp) {
    message.timestamp = Date.now();
  }

  if (!message.sessionId && currentSessionId) {
    message.sessionId = currentSessionId;
  }

  if (ws && ws.readyState === WebSocket.OPEN) {
    try {
      ws.send(JSON.stringify(message));
      metrics.messagesSent++;

      if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
        console.log('📤 WebSocket: Message sent:', message);
      }
    } catch (error) {
      console.error('❌ WebSocket: Error sending message:', error);
      metrics.errorCount++;
    }
  } else {
    // 连接未就绪，加入队列
    messageQueue.push(message);

    if (featureFlagManager.isEnabled('enable-websocket-debug-logs')) {
      console.log('📦 WebSocket: Message queued (connection not ready):', message);
    }
  }
}

/**
 * 添加事件监听器
 */
export function addEventListener(eventType: string, callback: (data: any) => void): () => void {
  if (!eventListeners.has(eventType)) {
    eventListeners.set(eventType, []);
  }
  
  eventListeners.get(eventType)!.push(callback);

  // 返回取消监听的函数
  return () => {
    const listeners = eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  };
}

/**
 * 获取连接状态
 */
export function getConnectionState(): ConnectionState {
  return connectionState;
}

/**
 * 获取当前会话ID
 */
export function getCurrentSessionId(): string | null {
  return currentSessionId;
}

/**
 * 获取连接指标
 */
export function getMetrics(): ConnectionMetrics {
  return { ...metrics };
}

/**
 * 检查连接是否活跃
 */
export function isConnected(): boolean {
  return connectionState === 'connected' && ws?.readyState === WebSocket.OPEN;
}

/**
 * 清理资源
 */
export function cleanup(): void {
  disconnect();
  eventListeners.clear();
  messageQueue.length = 0;
  
  // 重置指标
  metrics = {
    connectionCount: 0,
    messagesSent: 0,
    messagesReceived: 0,
    lastConnectedAt: null,
    lastDisconnectedAt: null,
    reconnectAttempts: 0,
    averageLatency: 0,
    errorCount: 0
  };
}

// 页面卸载时自动清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanup);
}
