// 🔥 第二阶段优化：前端日志通过HTTP批量上报，替代WebSocket传输
// 使用方式：在应用启动时调用 initializeClientLogger()

/* eslint-disable @typescript-eslint/no-explicit-any */

type Level = 'log' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: Level;
  message: string;
  timestamp: number;
}

// 🔥 日志缓冲区，定期批量发送
const buffer: LogEntry[] = [];

// 🔥 配置参数
const CONFIG = {
  BATCH_SIZE: 50,           // 批量发送的日志条数
  BATCH_INTERVAL: 30000,    // 批量发送间隔（30秒）
  MAX_BUFFER_SIZE: 500,     // 🔥 第三阶段优化：提高缓冲区大小，减少溢出警告
  API_ENDPOINT: '/api/logs/client-batch'
};

// 🔥 当前会话ID（用于关联日志）
let currentSessionId: string | null = null;

// 🔥 批量发送定时器
let batchTimer: NodeJS.Timeout | null = null;

// 🔥 第三阶段优化：重复日志过滤器
const recentMessages = new Map<string, number>();
const DUPLICATE_THRESHOLD = 5; // 5秒内相同消息只记录一次

// 🔥 添加日志到缓冲区
function addToBuffer(level: Level, message: string) {
  // 🔥 第三阶段优化：过滤重复日志，减少噪音
  const now = Date.now();
  const messageKey = `${level}:${message}`;
  const lastSeen = recentMessages.get(messageKey);

  if (lastSeen && (now - lastSeen) < DUPLICATE_THRESHOLD * 1000) {
    // 5秒内的重复消息，跳过
    return;
  }

  recentMessages.set(messageKey, now);

  // 🔥 清理过期的重复消息记录（每100条日志清理一次）
  if (buffer.length % 100 === 0) {
    const cutoff = now - DUPLICATE_THRESHOLD * 1000;
    for (const [key, timestamp] of recentMessages.entries()) {
      if (timestamp < cutoff) {
        recentMessages.delete(key);
      }
    }
  }

  const entry: LogEntry = {
    level: level === 'log' ? 'info' : level, // winston 后端不支持 .log 方法，转成 info
    message,
    timestamp: now
  };

  buffer.push(entry);

  // 🔥 如果缓冲区过大，立即发送
  if (buffer.length >= CONFIG.MAX_BUFFER_SIZE) {
    console.warn('⚠️ ClientLogger: Buffer overflow, sending logs immediately');
    flushBuffer();
  }
}

// 🔥 批量发送日志到后端
async function flushBuffer() {
  if (buffer.length === 0) return;

  const logsToSend = buffer.splice(0, CONFIG.BATCH_SIZE);

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('⚠️ ClientLogger: No auth token, skipping log upload');
      return;
    }

    const API_BASE_URL = process.env.NODE_ENV === 'production'
      ? 'https://mianshijun.xyz'
      : '';

    const response = await fetch(`${API_BASE_URL}${CONFIG.API_ENDPOINT}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        logs: logsToSend,
        sessionId: currentSessionId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    console.log(`📊 ClientLogger: Sent ${logsToSend.length} logs to backend`);
  } catch (error) {
    console.warn('⚠️ ClientLogger: Failed to send logs:', error);
    // 🔥 发送失败时，将日志重新放回缓冲区开头（但限制重试次数）
    buffer.unshift(...logsToSend.slice(0, 20)); // 只保留前20条，避免无限重试
  }
}

// 🔥 启动定时批量发送
function startBatchTimer() {
  if (batchTimer) return; // 防止重复启动

  batchTimer = setInterval(() => {
    if (buffer.length > 0) {
      flushBuffer();
    }
  }, CONFIG.BATCH_INTERVAL);

  console.log('📊 ClientLogger: Batch timer started');
}

// 🔥 停止定时批量发送
function stopBatchTimer() {
  if (batchTimer) {
    clearInterval(batchTimer);
    batchTimer = null;
    console.log('📊 ClientLogger: Batch timer stopped');
  }
}

// 🔥 全局覆盖 console 方法（只执行一次）
if (!(window as any).__clientLoggerInitialized) {
  const levels: Level[] = ['log', 'info', 'warn', 'error'];
  levels.forEach((level) => {
    const original = console[level] as (...args: any[]) => void;
    const override = (...args: any[]) => {
      // 保持原行为
      original.apply(console, args);

      // 拼接消息字符串
      const message = args
        .map((arg) => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        })
        .join(' ');

      // 🔥 添加到缓冲区而不是立即发送
      addToBuffer(level, message);
    };
    (console as any)[level] = override;
  });

  // 🔥 页面卸载时发送剩余日志
  window.addEventListener('beforeunload', () => {
    if (buffer.length > 0) {
      try {
        // 使用 sendBeacon 进行同步发送
        const token = localStorage.getItem('token');
        if (token) {
          const API_BASE_URL = process.env.NODE_ENV === 'production'
            ? 'https://mianshijun.xyz'
            : '';

          const data = JSON.stringify({
            logs: buffer.slice(0, 50), // 限制数量
            sessionId: currentSessionId
          });

          navigator.sendBeacon(`${API_BASE_URL}${CONFIG.API_ENDPOINT}`, data);
        }
      } catch {
        // ignore
      }
    }
  });

  (window as any).__clientLoggerInitialized = true;
}

// 🔥 第二阶段优化：初始化客户端日志器（替代WebSocket方式）
export function initializeClientLogger(sessionId?: string) {
  console.log('📊 ClientLogger: Initializing HTTP batch logger');

  if (sessionId) {
    currentSessionId = sessionId;
  }

  startBatchTimer();
}

// 🔥 设置会话ID（用于关联日志）
export function setSessionId(sessionId: string) {
  currentSessionId = sessionId;
  console.log(`📊 ClientLogger: Session ID set to ${sessionId}`);
}

// 🔥 手动刷新缓冲区（用于调试或特殊情况）
export function flushLogs() {
  console.log('📊 ClientLogger: Manual flush requested');
  flushBuffer();
}

// 🔥 清理资源
export function cleanupClientLogger() {
  stopBatchTimer();
  flushBuffer(); // 最后一次发送
  currentSessionId = null;
  console.log('📊 ClientLogger: Cleanup completed');
}

// 🔥 兼容性：保留旧的WebSocket接口，但改为使用HTTP
export function attachClientLoggerWebSocket(_ws: WebSocket) {
  console.warn('⚠️ ClientLogger: WebSocket attachment is deprecated, using HTTP batch instead');
  // 不再使用WebSocket，直接启动HTTP批量发送
  startBatchTimer();
}